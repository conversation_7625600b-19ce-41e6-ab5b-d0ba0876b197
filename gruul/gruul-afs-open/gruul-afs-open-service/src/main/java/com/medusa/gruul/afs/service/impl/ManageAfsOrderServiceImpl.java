package com.medusa.gruul.afs.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.afs.api.entity.AfsOrderItem;
import com.medusa.gruul.afs.api.enums.AfsOrderCloseTypeEnum;
import com.medusa.gruul.afs.api.enums.AfsOrderStatusEnum;
import com.medusa.gruul.afs.api.enums.AfsOrderTypeEnum;
import com.medusa.gruul.afs.api.model.UpdatePackageOrderStatusMessage;
import com.medusa.gruul.afs.controller.enums.ExternalOrderEnum;
import com.medusa.gruul.afs.mapper.AfsOrderMapper;
import com.medusa.gruul.afs.model.*;
import com.medusa.gruul.afs.mp.Sender;
import com.medusa.gruul.afs.mp.model.AfsRemoveDeliverOrderMessage;
import com.medusa.gruul.afs.mp.model.BaseAfsOrderMessage;
import com.medusa.gruul.afs.service.IAfsNegotiateHistoryService;
import com.medusa.gruul.afs.service.IAfsOrderItemService;
import com.medusa.gruul.afs.service.IManageAfsOrderService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.goods.api.param.OperateStockDto;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.entity.OrderSetting;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.vo.RelationInfoVo;
import com.medusa.gruul.platform.api.model.vo.StoreFrontOrderVo;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 售后工单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Slf4j
@Service
public class ManageAfsOrderServiceImpl extends ServiceImpl<AfsOrderMapper, AfsOrder> implements IManageAfsOrderService {

    @Resource
    private IAfsOrderItemService afsOrderItemService;
    @Resource
    private RemoteOrderService orderService;
    @Resource
    private Sender sender;
    @Resource
    private IAfsNegotiateHistoryService negotiateHistoryService;
    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private RemoteShopsService remoteShopsService;
    @Resource
    private RemoteOrderService remoteOrderService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;



    /**
     * 商家拒绝
     *
     * @param dto
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:33
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sellerRefuse(SellerRefusalDto dto) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        AfsOrder afsOrder = this.getById(dto.getAfsId());
        if (ObjectUtil.isNull(afsOrder)) {
            throw new ServiceException("没有符合条件的售后单，请刷新后重试");
        }
        if (afsOrder.getStatus().waitSellerApprove()) {
            afsOrder.setStatus(AfsOrderStatusEnum.CLOSE);
            afsOrder.setCloseType(AfsOrderCloseTypeEnum.SELLER_REFUSE);
            afsOrder.setCloseTime(LocalDateTime.now());
            afsOrder.setRefusalReason(dto.getRefusalReason());
            afsOrder.setDeadline(null);
            this.updateById(afsOrder);

            //解冻用户通惠证记录
            Order order = remoteOrderService.getOrderById(afsOrder.getReceiptBillId());
            if(order==null){
                throw new ServiceException("订单不存在！");
            }
            //商家拒绝申请，根据订单id解冻用户通惠证记录
            if(order.getType().equals(OrderTypeEnum.TICKET)){
                Boolean b = remoteMiniAccountService.thawAccountPassTicket(order.getId());
                if(b){
                    log.info("解冻用户通惠证记录成功");
                }
            }
            //商家拒绝申请，权益包订单恢复状态
            if(order.getMallOrderType() == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
                UpdatePackageOrderStatusMessage message = new UpdatePackageOrderStatusMessage();
                message.setOrderId(order.getId()+"");
                message.setStatus(null);
                sender.sendUpdatePackageOrderStatusMessage(message);
            }
            negotiateHistoryService.sellerRefuse(afsOrder);
            ShopContextHolder.setShopId(shopId);
        } else {
            throw new ServiceException("没有符合条件的售后单，请刷新后重试");
        }

    }

    /**
     * 商家同意
     *
     * @param afsId
     * @param isSystem
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:34
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sellerApprove(Long afsId, Boolean isSystem) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        AfsOrder afsOrder = this.getById(afsId);
        if (ObjectUtil.isNull(afsOrder)) {
            throw new ServiceException("没有符合条件的售后单，请刷新后重试");
        }
        if (afsOrder.getStatus().waitSellerApprove()) {
            //按照售后类型检查是否满足申请条件
            switch (afsOrder.getType()) {
                case REFUND:
                    //直接退款
                    userApplyRefund(afsOrder, isSystem);
                    break;
                case RETURN_REFUND:
                    //检查订单状态。如果未签收，直退款，已签收需先退货
                    userApplyReturn(afsOrder, isSystem, false);
                    break;
                default:
                    throw new ServiceException("不是有效的售后类型");
            }
            //关闭其他正在进行的售后单和换货单
            closeAfsOrderAndExChangeOrder(afsOrder);
            //更新售后订单经手人，部门
            CurUserDto curUserDto = CurUserUtil.getHttpCurUser();

            RelationInfoVo relationInfoVo = remoteMiniInfoService.getRelationInfoByAccountId(curUserDto.getUserId());

            afsOrder.setDepartmentId(relationInfoVo.getDepartmentId());//部门id
            afsOrder.setDepartmentCode(relationInfoVo.getDepartmentCode());//部门标识
            afsOrder.setDepartmentName(relationInfoVo.getDepartmentName());//部门名称

            afsOrder.setEmployeeId(relationInfoVo.getEmployeeId());//职员id
            afsOrder.setEmployeeOutId(relationInfoVo.getEmployeeOutId());//职员标识
            afsOrder.setEmployeeName(relationInfoVo.getEmployeeName());//职员名称

            afsOrder.setStoreFrontId(relationInfoVo.getStoreFrontId());//门店id
            afsOrder.setStoreFrontCode(relationInfoVo.getStoreFrontCode());//门店标识
            afsOrder.setStoreFrontName(relationInfoVo.getStoreFrontName());//门店名称

            afsOrder.setAccountId(relationInfoVo.getAccountId());//用户id
            afsOrder.setAccountName(relationInfoVo.getAccountName());//用户名称

            afsOrder.setStockId(relationInfoVo.getStockId());
            afsOrder.setStockCode(relationInfoVo.getStockCode());
            afsOrder.setStockName(relationInfoVo.getStockName());

            afsOrder.setSendStatus(0);
            //判断订单为普通订单，则需要同步退货单到易达aio
            LambdaQueryWrapper<AfsOrderItem>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AfsOrderItem::getAfsId,afsId);
            List<AfsOrderItem> list = afsOrderItemService.list(wrapper);
            if(list!=null&&list.size()>0){
                AfsOrderItem afsOrderItem = list.get(0);
                Long orderId = afsOrderItem.getOrderId();
                Order order = remoteOrderService.getOrderById(orderId);
                if(order.getMallOrderType() == ProductTypeEnum.BASIC_PRODUCT.getStatus() || order.getMallOrderType() == ProductTypeEnum.GROUP_PRODUCT.getStatus()){
                    afsOrder.setReturnSendStatus(0);
                }else{
                    afsOrder.setReturnSendStatus(1);
                }
            }

            this.updateById(afsOrder);
            ShopContextHolder.setShopId(shopId);
        } else {
            throw new ServiceException("没有符合条件的售后单，请刷新后重试");
        }
    }


    /**
     * 根据退货单回填库存
     *
     * @param afsOrder
     * @return void
     * <AUTHOR>
     * @date 2022/6/14 14:34
     */
    @Transactional(rollbackFor = Exception.class)
    void backfillInventory(AfsOrder afsOrder) {
        //查询这个售后订单的详细信息

        List<AfsOrderItem> afsOrderItemList = afsOrderItemService.list(new LambdaQueryWrapper<AfsOrderItem>()
                .eq(AfsOrderItem::getAfsId, afsOrder.getId())
        );
        Order order=orderService.getOrderById(afsOrder.getReceiptBillId());



        if(order.getType().equals(OrderTypeEnum.TICKET)){

            for (AfsOrderItem afsOrderItem : afsOrderItemList) {
                Long result = remoteShopsService.passTicketAddStock(afsOrderItem.getProductSkuId());
                if(result==null){
                    throw new ServiceException("返回库存失败");
                }
            }
            //根据订单id删除用户通惠证表
            Boolean b = remoteMiniAccountService.deleteAccountPassTicket(order.getId());
            if(b){
                log.info("删除用户通惠证成功！");
            }
        }else{
            List<OperateStockDto> list=new ArrayList<>();
            for (AfsOrderItem afsOrderItem : afsOrderItemList) {
                OperateStockDto operateStockDto=new OperateStockDto(afsOrderItem.getProductSkuId(),afsOrderItem.getProductQuantity(),order.getWarehouseId());
                list.add(operateStockDto);
            }
            boolean fl= remoteGoodsService.batchRevertStock(list);
            if(!fl){
                throw new ServiceException("返回库存失败");
            }
        }

    }


    /**
     * 关闭订单和换货单
     *
     * @param afsOrder
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:34
     */
    private void closeAfsOrderAndExChangeOrder(AfsOrder afsOrder) {
        //查询这个售后包含的其他订单
        List<AfsOrderItem> itemList =
                afsOrderItemService.list(new LambdaQueryWrapper<AfsOrderItem>()
                        .eq(AfsOrderItem::getAfsId, afsOrder.getId())
                );
        if (itemList.isEmpty()) {
            return;
        }
        //获得订单ID的集合
        String originalOrderIds =
                itemList.stream().map(AfsOrderItem::getOrderId).map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA));
        //查询这些订单是否有其他正在进行的售后单
        List<AfsOrder> afsOrderList =
                baseMapper.selectProgressByOriginalOrderIdsAndIdNotIn(originalOrderIds,
                        afsOrder.getId());
        if (afsOrderList.isEmpty()) {
            return;
        }
        //如果上一步的售后单不为空
        for (AfsOrder order : afsOrderList) {
            //如果其他售后单的类型和当前售后单类型相同，或者其他售后单的列席不是换货单，关闭其他售后单，原因为重新换货
            if (order.getType().equals(afsOrder.getType())) {
                order.setStatus(AfsOrderStatusEnum.CLOSE);
                order.setCloseType(AfsOrderCloseTypeEnum.RE_APPLY);
                order.setCloseTime(LocalDateTime.now());
                order.setDeadline(null);
                baseMapper.updateById(order);
            }
        }
        itemList =
                afsOrderItemService.list(new LambdaQueryWrapper<AfsOrderItem>()
                        .in(AfsOrderItem::getAfsId, afsOrderList.stream()
                                .map(AfsOrder::getId)
                                .collect(Collectors.toSet())));

        List<Long> orderIds = itemList.stream().map(AfsOrderItem::getOrderId).collect(Collectors.toList());
        //关闭换货订单
        orderService.closeExchangeOrder(orderIds);
        for (Long orderId : orderIds) {
            //关闭签收单
            closeReceipt(orderId, afsOrder.getId());
        }
    }


    /**
     * 备注售后单
     *
     * @param dto
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:34
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void note(SellerNoteDto dto) {
        String note = "";
        List<AfsOrder> records = baseMapper.selectBatchIds(dto.getAfsIds());
        for (AfsOrder record : records) {
            if (StrUtil.isBlank(dto.getNote())) {
                note = "";
            } else {
                note = dto.getNote();
            }
            if (!dto.getOver() && StrUtil.isNotBlank(record.getNote())) {
                record.setNote(record.getNote() + StrUtil.CRLF + note);
            } else {
                record.setNote(note);
            }
            baseMapper.updateById(record);
        }
    }

    /**
     * 用户申请退款
     *
     * @param afsOrder
     * @param isSystem
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:35
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userApplyRefund(AfsOrder afsOrder, boolean isSystem) {
        //获取退款金额和原订单信息
        List<AfsOrderItem> afsOrderItemList = afsOrderItemService.list(new LambdaQueryWrapper<AfsOrderItem>()
                .eq(AfsOrderItem::getAfsId, afsOrder.getId()));


        for (AfsOrderItem orderItem : afsOrderItemList) {
            //调用退款流程
            orderService.closeOrder(orderItem.getAfsId(), orderItem.getRefundAmount(), afsOrder.getType().getCode(),
                    orderItem.getOrderId());

            List<Long> orderIds = afsOrderItemService.getExchangeOrder(orderItem.getOrderId());
            orderService.closeExchangeOrder(orderIds);
            //添加协商历史
            negotiateHistoryService.refund(afsOrder, isSystem);
            //更新退款单的的签收单状态
            OrderVo orderVo = orderService.orderInfo(orderItem.getOrderId());
            log.info("OrderVo is {}", JSONUtil.toJsonStr(orderVo));
            if (OrderStatusEnum.isClose(orderVo.getStatus())) {
                closeReceipt(orderItem.getOrderId(), orderItem.getAfsId());
            }
        }
        //更改售后单状态
        afsOrder.setStatus(AfsOrderStatusEnum.SUCCESS);
        afsOrder.setSuccessTime(LocalDateTime.now());
        afsOrder.setDeadline(null);
        this.updateById(afsOrder);
        backfillInventory(afsOrder);
        //发送微信退款消息
        if (afsOrder.getType().equals(AfsOrderTypeEnum.REFUND) || afsOrder.getType().equals(AfsOrderTypeEnum.RETURN_REFUND)) {
            AfsOrderVo afsOrderVo = new AfsOrderVo();
            BeanUtil.copyProperties(afsOrder, afsOrderVo);
            afsOrderVo.setItem(afsOrderItemList);
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(afsOrder.getUserId(),
                    Collections.singletonList(4));
            sender.sendWechatRefundMessage(afsOrderVo, accountInfoDto.getMiniAccountOauths().getOpenId());
        }

    }


    /**
     * 关闭签收单
     *
     * @param orderId
     * @param afsId
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:35
     */
    private void closeReceipt(Long orderId, Long afsId) {
        List<AfsOrder> afsOrderList = baseMapper.selectProgressByOrderIdAndIdNotIn(orderId, afsId);
        if (afsOrderList.isEmpty()) {
            AfsRemoveDeliverOrderMessage message = new AfsRemoveDeliverOrderMessage();
            message.setOrderId(orderId);
            sender.sendRemoveSendBillOrderMessage(message);
        }

    }


    /**
     * 用户申请退货
     *
     * @param afsOrder
     * @param isSystem
     * @param fromLeaderApprove
     * @return void
     * <AUTHOR>
     * @date 2021/3/17 22:36
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userApplyReturn(AfsOrder afsOrder, Boolean isSystem, boolean fromLeaderApprove) {
        //获取订单设置
        OrderSetting orderSetting = orderService.getOrderSetting();
        //获取原订单信息
        List<AfsOrderItem> afsOrderItemList = afsOrderItemService.list(new LambdaQueryWrapper<AfsOrderItem>()
                .eq(AfsOrderItem::getAfsId, afsOrder.getId()));
        OrderVo orderVo = orderService.orderInfo(CollUtil.getFirst(afsOrderItemList).getOrderId());
        //不需要经过退货
        boolean unwantedReturn;
        /*if (afsOrder.getIsLogistics()) {
            //待商家确认收货的不需要退货
            unwantedReturn = afsOrder.getStatus().isBusinessReceipted();
        } else {
            unwantedReturn = fromLeaderApprove;
        }*/
        //待商家确认收货的不需要退货
        unwantedReturn = afsOrder.getStatus().isBusinessReceipted();
        //如果不符合这些条件则需要退货vc
        if (!unwantedReturn) {
            afsOrder.setDeadline(LocalDateTimeUtil.now().plusDays(orderSetting.getUserReturnOvertime()));
            afsOrder.setStatus(AfsOrderStatusEnum.WAIT_FOR_RETURN);
            this.updateById(afsOrder);
            //添加协商历史
            negotiateHistoryService.sellerApproveNeedReturn(afsOrder, isSystem,
                    null,
                    afsOrder.getIsLogistics());
            //添加用户操作倒计时
            BaseAfsOrderMessage message = new BaseAfsOrderMessage();
            message.setId(afsOrder.getId());
            if (afsOrder.getType().equals(AfsOrderTypeEnum.REFUND) || afsOrder.getType().equals(AfsOrderTypeEnum.RETURN_REFUND)) {
                AfsOrderVo afsOrderVo = new AfsOrderVo();
                BeanUtil.copyProperties(afsOrder, afsOrderVo);
                afsOrderVo.setItem(afsOrderItemList);
                AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(afsOrder.getUserId(),
                        Collections.singletonList(4));
                sender.sendWechatReturnMessage(afsOrderVo, accountInfoDto.getMiniAccountOauths().getOpenId());
            }
        } else {
            userApplyRefund(afsOrder, isSystem);
        }
    }

    /**
     * 分页查询售后单
     *
     * @param dto
     * @return com.medusa.gruul.common.core.util.PageUtils<com.medusa.gruul.afs.model.ManageAfsOrderVo>
     * <AUTHOR>
     * @date 2021/3/17 22:36
     */
    @Override
    public PageUtils<ManageAfsOrderVo> searchManageAfsOrderVoPage(SearchDto dto) {
        List<AfsOrderStatusEnum> statusList = new ArrayList<>();
        //订单状态 -1：所有订单, 0.待处理, 1.处理中, 2.已完成, 3.已关闭
        switch (dto.getStatus()) {
            case -1:
                statusList.clear();
                break;
            case 0:
                statusList.add(AfsOrderStatusEnum.WAIT_FOR_BUSINESS_APPROVED);
                break;
            case 1:
                statusList.add(AfsOrderStatusEnum.WAIT_FOR_RETURN);
                statusList.add(AfsOrderStatusEnum.WAIT_FOR_SEND);
                statusList.add(AfsOrderStatusEnum.SHIPPED);
                statusList.add(AfsOrderStatusEnum.WAIT_FOR_BUSINESS_RECEIPT);
                break;
            case 2:
                statusList.add(AfsOrderStatusEnum.SUCCESS);
                break;
            case 3:
                statusList.add(AfsOrderStatusEnum.CLOSE);
                break;
            default:
                break;
        }
        setDefaultTime(dto);

        String shopId = ShopContextHolder.getShopId();
        List<String> shopIds = new ArrayList<>();
        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
        Integer mainFlag = shopsPartner.getMainFlag();
        if(mainFlag==1){
            //获取特殊配置
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                Integer platformSearchShopOrder = specialSetting.getPlatformSearchShopOrder();
                if(platformSearchShopOrder==1){
                    shopIds = remoteMiniInfoService.getSpecialSettingShopIds(shopId);
                    ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
                }
            }
        }

        shopIds.add(shopId);
        dto.setShopIds(shopIds);
        if(StringUtil.isNotEmpty(dto.getQueryShopIds())){
            List<String> shopIds2 = new ArrayList<>();
            for (String shopId2 : dto.getQueryShopIds().split(",")) {
                shopIds2.add(shopId2);
            }
            dto.setShopIds2(shopIds2);
        }


        Page<ManageAfsOrderVo> page = baseMapper.searchManageAfsOrderVoPage(new Page(dto.getCurrent(), dto.getSize()),
                statusList, dto);
        List<ManageAfsOrderVo> records = page.getRecords();
        if(records!=null&&records.size()>0){
            for (ManageAfsOrderVo manageAfsOrderVo : records) {
                if(StringUtil.isNotEmpty(manageAfsOrderVo.getShopId())){
                    ShopsPartner shopsPartner1 = remoteShopsService.getByShopId(Long.valueOf(manageAfsOrderVo.getShopId()));
                    if(shopsPartner1!=null&&!shopsPartner1.equals("")){
                        manageAfsOrderVo.setShopName(shopsPartner1.getName());
                    }
                }
                Long id = manageAfsOrderVo.getId();
                LambdaQueryWrapper<AfsOrderItem>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfsOrderItem::getDeleted,CommonConstants.NUMBER_ZERO);
                wrapper.eq(AfsOrderItem::getAfsId,id);
                List<AfsOrderItem> list = afsOrderItemService.list(wrapper);
                List<ManageAfsOrderItemVo>itemList = new ArrayList<>();
                for (AfsOrderItem afsOrderItem : list) {
                    ManageAfsOrderItemVo manageAfsOrderItemVo = new ManageAfsOrderItemVo();
                    BeanUtil.copyProperties(afsOrderItem,manageAfsOrderItemVo);
                    itemList.add(manageAfsOrderItemVo);
                }
                manageAfsOrderVo.setItemList(itemList);

            }
        }
        ShopContextHolder.setShopId(shopId);
        return new PageUtils(page);
    }

    @Override
    public void updateAfsReceiveSyncStatus(List<Long> ids, String receiveSyncStatus) {
        baseMapper.updateReceiveSyncStatus(ids,receiveSyncStatus);
    }

    @Override
    public void updateAfsReceiveSyncReturnStatus(List<Long> ids, String receiveSyncReturnStatus) {
        baseMapper.updateReceiveSyncReturnStatus(ids,receiveSyncReturnStatus);
    }

    @Override
    public PageUtils<OutAfsReceiveOrderVo> searchOutAfsReceiveOrder(OutAfsReceiveOrderParam param) {
        Page<OutAfsReceiveOrderVo> page = this.baseMapper.searchOutAfsReceiveOrder(new Page(param.getCurrent(), param.getSize()),param);
        List<OutAfsReceiveOrderVo> list = page.getRecords();
        List<Long>idList=list.stream().map(OutAfsReceiveOrderVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateAfsReceiveSyncStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils<OutAfsRefundOrderVo> searchOutAfsRefundOrder(OutAfsRefundOrderParam param) {
        Page<OutAfsRefundOrderVo> page = this.baseMapper.searchOutAfsRefundOrder(new Page(param.getCurrent(), param.getSize()),param);
        List<OutAfsRefundOrderVo> list = page.getRecords();
        List<Long>idList=list.stream().map(OutAfsRefundOrderVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateAfsReceiveSyncReturnStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    private void setDefaultTime(SearchDto dto) {
        if (StrUtil.isBlank(dto.getStartTime())) {
            dto.setStartTime(DateUtil.beginOfMonth(new Date()).toString());
        } else {
            dto.setStartTime(dto.getStartTime() + " 00:00:00");
        }
        if (StrUtil.isBlank(dto.getEndTime())) {
            dto.setEndTime(DateUtil.endOfMonth(new Date()).toString());
        } else {
            dto.setEndTime(dto.getEndTime() + " 23:59:59");
        }
    }

    @Override
    public void exportManageAfsOrder(SearchDto dto) {
        // 设置导出最大数量
        HuToolExcelUtils.exportParamToMax(dto);
        // 直接调用现有的查询方法
        PageUtils<ManageAfsOrderVo> pageUtils = this.searchManageAfsOrderVoPage(dto);
        List<ManageAfsOrderVo> afsOrderList = pageUtils.getList();

        HuToolExcelUtils.exportData(afsOrderList, "售后工单列表",
                item->{
                    ManageAfsOrderExcelVo excelVo = new ManageAfsOrderExcelVo();
                    BeanUtils.copyProperties(item, excelVo);

                    // 处理工单类型
                    if (item.getType() != null) {
                        excelVo.setType(item.getType().getDesc());
                    }

                    // 处理配送方式
                    if (item.getDeliveryType() != null) {
                        excelVo.setDeliveryType(item.getDeliveryType().getDesc());
                    }

                    // 处理售后状态
                    if (item.getStatus() != null) {
                        excelVo.setStatus(item.getStatus().getDesc());
                    }

                    // 处理订单状态
                    if (item.getOrderStatus() != null) {
                        excelVo.setOrderStatus(item.getOrderStatus().getDesc());
                    }

                    // 处理订单类型
                    if (item.getOrderType() != null) {
                        excelVo.setOrderType(item.getOrderType().getDesc());
                    }

                    // 拼接收货地址
                    StringBuilder address = new StringBuilder();
                    if (StrUtil.isNotBlank(item.getReceiverProvince())) {
                        address.append(item.getReceiverProvince());
                    }
                    if (StrUtil.isNotBlank(item.getReceiverCity())) {
                        address.append(item.getReceiverCity());
                    }
                    if (StrUtil.isNotBlank(item.getReceiverRegion())) {
                        address.append(item.getReceiverRegion());
                    }
                    if (StrUtil.isNotBlank(item.getReceiverDetailAddress())) {
                        address.append(item.getReceiverDetailAddress());
                    }
                    excelVo.setReceiverAddress(address.toString());

                    // 格式化创建时间
                    if (item.getCreateTime() != null) {
                        excelVo.setCreateTime(DateUtil.format(item.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                    }

                    return excelVo;
                });
    }
}
