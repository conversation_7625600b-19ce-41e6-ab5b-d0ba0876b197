package com.medusa.gruul.afs.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: AI Assistant
 * @Description: 售后工单导出Excel VO类
 * @Date: Created in 2025-01-04
 */
@Data
@ApiModel(value = "售后工单导出Excel VO类")
public class ManageAfsOrderExcelVo {

    @ApiModelProperty(value = "序号")
    private Long index;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "订单号")
    private Long orderId;

    @ApiModelProperty(value = "售后类型")
    private String afterType;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "申请时间")
    private String createTime;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "数量")
    private Integer productQuantity;

    @ApiModelProperty(value = "金额")
    private BigDecimal productAmount;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否关闭")
    private String close;

}
