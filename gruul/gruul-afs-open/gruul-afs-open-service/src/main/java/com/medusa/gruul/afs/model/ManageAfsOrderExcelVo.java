package com.medusa.gruul.afs.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: AI Assistant
 * @Description: 售后工单导出Excel VO类
 * @Date: Created in 2025-01-04
 */
@Data
@ApiModel(value = "售后工单导出Excel VO类")
public class ManageAfsOrderExcelVo {

    @ApiModelProperty(value = "序号")
    private Long index;

    @ApiModelProperty(value = "工单ID")
    private Long id;

    @ApiModelProperty(value = "工单编号")
    private String no;

    @ApiModelProperty(value = "工单类型")
    private String type;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品规格")
    private String specs;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "商品数量")
    private Integer productQuantity;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;

    @ApiModelProperty(value = "收货地址")
    private String receiverAddress;

    @ApiModelProperty(value = "配送方式")
    private String deliveryType;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "售后状态")
    private String status;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "备注")
    private String note;

}
