package com.medusa.gruul.order.controller.manage;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.logistics.api.feign.RemoteLogisticsFeginService;
import com.medusa.gruul.order.api.constant.OrderShareSettingRedisKey;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.entity.OrderSetting;
import com.medusa.gruul.order.api.entity.OrderShareSetting;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.*;


/**
 * 城市合伙人PC端
 *
 * <AUTHOR>
 * @date 2019/11/13 20:20
 */
@Slf4j
@RestController
@RequestMapping("/manage")
@Api(tags = "城市合伙人PC端订单接口")
public class ManageOrderController {
    private static final String sname = "{sname}";
    @Resource
    private IManageOrderService orderService;
    @Resource
    private IOrderSettingService orderSettingService;
    @Resource
    private IOrderShareSettingService orderShareSettingService;
    @Resource
    private IOrderDeliveryService orderDeliveryService;
    @Resource
    private RemoteLogisticsFeginService remoteLogisticsFeginService;
    @Resource
    private IOrderItemService orderItemService;


    @ApiOperation(value = "商家查询订单", notes = "商家查询订单")
    @GetMapping("/search")
    public Result searchOrder(@Validated ManageSearchOrderDto dto) {
        PageUtils page = orderService.searchOrder(dto);
        return Result.ok(page);
    }

    @GetMapping("/export/order")
    @ApiOperation(value = "导出订单")
    public void exportOrder(@Validated ManageSearchOrderDto dto){
        orderService.exportOrder(dto);
    }

    @ApiOperation(value = "商家查询订单明细", notes = "商家查询订单明细")
    @PostMapping("/searchOrderDetail")
    public Result searchOrderDetail(@RequestBody ManageOrderItemParam params) {
        PageUtils page = orderService.searchOrderDetail(params);
        return Result.ok(page);
    }

    @GetMapping("/export/orderDetail")
    @ApiOperation(value = "导出订单明细")
    public void exportOrderDetail(ManageOrderItemParam params){
        orderService.exportOrderDetail(params);
    }

    /**
     * 外部系统查询订单-出库单
     * @param dto
     * @return
     */
    @ApiOperation(value = "外部系统查询订单", notes = "外部系统查询订单-出库单")
    @PostMapping("/externalSearch")
    public Result externalSearchOrder(@Validated ManageSearchOrderDto dto) {
        PageUtils list=orderService.searchExternalOrder(dto);
        return Result.ok(list);
    }

    /**
     * 外部系统查询订单-收款单
     * @param dto
     * @return
     */
    @ApiOperation(value = "外部系统查询订单-收款单", notes = "外部系统查询订单-收款单")
    @PostMapping("/externalSearchOrderByReceived")
    public Result externalSearchOrderByReceived(@Validated ManageSearchOrderDto dto) {
        PageUtils list=orderService.searchExternalOrderByReceived(dto);
        return Result.ok(list);
    }

    @ApiOperation(value = "商家查询订单", notes = "商家查询订单")
    @GetMapping("/overview")
    public Result<ManageOrderOverviewVo> getOverview() {
        ManageOrderOverviewVo vo = orderService.getOverview();
        return Result.ok(vo);
    }

    @ApiOperation(value = "实时概况", notes = "实时概况")
    @GetMapping("/realTimeOverview")
    public Result<ManageOrderRealTimeOverviewVo> getRealTimeOverview(@RequestParam(value = "startDate") String  startDate,@RequestParam(value = "endDate") String endDate,@RequestParam(value = "type") Integer type) throws ParseException {
        ManageOrderRealTimeOverviewVo vo = orderService.getRealTimeOverview(startDate,endDate, type);
        return Result.ok(vo);
    }

    @ApiOperation(value = "获取商品排行", notes = "获取商品排行")
    @GetMapping("/getProductRanking")
    public Result<Map> getProductRanking() throws ParseException {
        HashMap<String,List<ProductRankingDto>> map=orderItemService.getProductRanking();
        return Result.ok(map);
    }


    @ApiOperation(value = "是否开启评论", notes = "是否开启评论")
    @GetMapping("/evaluate/setting")
    public Result getSetting() {
        OrderSetting orderSetting = orderSettingService.getOne(null);
        return Result.ok(orderSetting.getOpenEvaluate());
    }

    @ApiOperation(value = "设置是否开启评论", notes = "设置是否开启评论")
    @PutMapping("/evaluate/setting")
    public Result setting(@RequestParam(value = "openEvaluate") Boolean openEvaluate) {
        OrderSetting orderSetting = orderSettingService.getOne(null);
        orderSetting.setOpenEvaluate(openEvaluate);
        orderSettingService.updateById(orderSetting);
        return Result.ok();
    }

    @ApiOperation("查询订单设置")
    @GetMapping("/setting")
    public Result<OrderSetting> getOrderSetting() {
        OrderSetting orderSetting = orderSettingService.getOne(null);
        return Result.ok(orderSetting);
    }

    @ApiOperation("设置订单设置")
    @PostMapping("/setting")
    public Result<OrderSetting> setOrderSetting(@RequestBody @NotNull @Validated OrderSetting setting) {
        OrderSetting orderSetting = orderSettingService.update(setting);
        return Result.ok(orderSetting);
    }

    @ApiOperation(value = "获取当前分享晒单设置", notes = "晒单设置")
    @GetMapping("/share/setting")
    public Result getShareSetting() {
        OrderShareSetting orderShareSetting = orderShareSettingService.getOne(null);
        return Result.ok(orderShareSetting);
    }

    @ApiOperation(value = "设置分享晒单设置", notes = "晒单设置")
    @PutMapping("/share/setting")
    public Result setShareSetting(@RequestBody @Validated OrderShareSettingVo vo) {
        OrderShareSettingRedisKey redisKey = new OrderShareSettingRedisKey();
        //分享写死了name
        vo.setTitle("{sname}");
        vo.setBackground(" ");
        OrderShareSetting orderShareSetting = orderShareSettingService.getOne(null);
        if(null == orderShareSetting){
            orderShareSetting = new OrderShareSetting();
        }
        if (vo.isDefaultValue()) {
            orderShareSetting.setBackground(OrderShareSetting.DEFAULT_BACKGROUND);
            orderShareSetting.setTitle(OrderShareSetting.DEFAULT_TITLE);
        } else {
            if (StrUtil.count(vo.getTitle(), sname) != 1) {
                throw new ServiceException("标题必须且只能包含一个收货人信息");
            }
            orderShareSetting.setBackground(vo.getBackground());
            orderShareSetting.setTitle(vo.getTitle());
        }
        orderShareSettingService.saveOrUpdate(orderShareSetting);
        return Result.ok(orderShareSetting);
    }

    @ApiOperation(value = "根据运单号查询物流轨迹", notes = "根据运单号查询物流轨迹")
    @GetMapping("/traces")
    public Result tracesInfo(@RequestParam(value = "tracesId") String tracesId,
                             @RequestParam(value = "deliveryCode") String deliveryCode) {
        if(StrUtil.isBlank(tracesId) || StrUtil.isBlank(deliveryCode)){
            return Result.ok();
        }
        Result logisticsExpressInfo = remoteLogisticsFeginService.getLogisticsExpressInfo(tracesId, deliveryCode);
        return Result.ok(logisticsExpressInfo.getData());
    }

    @ApiOperation(value = "商家查询订单详情", notes = "用户查询订单详情")
    @GetMapping("/info/{orderId}")
    public Result<OrderVo> orderInfo(@PathVariable(value = "orderId") @NotNull Long orderId) {
        OrderVo orderVo = orderService.orderInfo(orderId);
        return Result.ok(orderVo);
    }

    @ApiOperation(value = "商家批量发送发货提醒", notes = "商家批量发送发货提醒")
    @PutMapping("/deliverMessage")
    public Result deliverMessage(@RequestBody Long[] orderIds) {
        orderService.deliverMessage(orderIds);
        return Result.ok();
    }

    @ApiOperation(value = "商家批量关闭订单", notes = "商家批量关闭订单")
    @PutMapping("/close")
    public Result closeOrder(@RequestBody Long[] orderIds) {
        orderService.closeOrder(Arrays.asList(orderIds));
        return Result.ok();
    }
    @ApiOperation(value = "商家批量处理线下支付订单", notes = "商家批量处理线下支付订单")
    @PutMapping("/payment")
    public Result paymentOrder(@RequestBody Long[] orderIds) {
        orderService.paymentOrder(Arrays.asList(orderIds));
        return Result.ok();
    }

    @ApiOperation(value = "商家批量修改订单发送状态", notes = "商家批量修改订单发送状态")
    @PutMapping("/updateSendStatus")
    public Result updateSendStatus(@RequestBody List<Long> orderIds, @RequestParam(value = "sendStatus") String sendStatus) {
        orderService.updateSendStatus(orderIds,sendStatus);
        return Result.ok();
    }

    @ApiOperation(value = "商家批量备注订单", notes = "商家批量备注订单")
    @PutMapping("/note")
    public Result noteOrder(@RequestBody Long[] orderIds, @RequestParam(value = "over") Boolean over,
                            @RequestParam(value = "note", required = false) String note) {
        orderService.noteOrder(Arrays.asList(orderIds), note, over);
        return Result.ok();
    }

    @ApiOperation(value = "商家后台修改收货地址", notes = "商家后台修改收货地址")
    @PutMapping("/receiver/address")
    public Result updateReceiverAddress(@RequestBody ReceiverAddressDto dto) {
        orderDeliveryService.updateReceiverAddress(dto);
        return Result.ok();
    }


    @ApiOperation(value = "商家查看评价列表", notes = "商家查看评价列表")
    @GetMapping("/evaluate/search")
    public Result searchOrderEvaluate(@Validated ManageSearchEvaluateDto dto) {
        PageUtils page = orderService.searchOrderEvaluate(dto);
        return Result.ok(page);
    }

    @ApiOperation(value = "商家导出评价列表", notes = "商家导出评价列表")
    @GetMapping("/evaluate/export")
    public Result exportOrderEvaluate(@Validated ManageSearchEvaluateDto dto) {
        orderService.exportOrderEvaluate(dto);
        return Result.ok();
    }

    @ApiOperation(value = "商家设置评价为精选", notes = "商家设置评价为精选")
    @PutMapping("/choice/evaluate")
    public Result choiceEvaluate(@RequestBody Long[] ids) {
        orderService.choiceEvaluate(Arrays.asList(ids));
        return Result.ok();
    }

    @ApiOperation(value = "商家取消设置评价为精选", notes = "商家取消设置评价为精选")
    @PutMapping("/unChoice/evaluate")
    public Result unChoiceEvaluate(@RequestBody Long[] ids) {
        orderService.unChoiceEvaluate(Arrays.asList(ids));
        return Result.ok();
    }

    @ApiOperation(value = "商家回复评价", notes = "商家回复评价")
    @PutMapping("/reply/evaluate")
    public Result replyEvaluate(@RequestParam(value = "id") Long id, @RequestParam(value = "reply") String reply) {
        orderService.replyEvaluate(id, reply);
        return Result.ok();
    }

    @Deprecated
    @ApiOperation(value = "快递订单搜索", notes = "快递订单搜索")
    @GetMapping("/reply/searchDeliveryOrders")
    public Result searchLogisticsOrderList(ManageLogisticsOrderDto manageLogisticsOrderDto) {
        PageUtils pageUtils = orderService.searchLogisticsOrderList(manageLogisticsOrderDto);
        return Result.ok(pageUtils);
    }

    @ApiOperation(value = "快递订单搜索", notes = "快递订单")
    @GetMapping("/search/logistics")
    public Result searchLogisticsOrder(ManageSearchLogisticsOrderDto dto) {
        List<ManageOrderVo> list = orderService.searchLogisticsOrder(dto);
        return Result.ok(list);
    }

    @ApiOperation(value = "查询是否有待发货的快递订单", notes = "快递订单")
    @GetMapping("/logistics/wait/send")
    public Result hasWaitSend() {
        Integer hasWaitSend = orderService.countLogisticsWaitSend();
        return Result.ok(hasWaitSend);
    }

    @ApiOperation(value = "无需快递的物流发货", notes = "快递订单")
    @PutMapping("/logistics/send")
    public Result logisticsSend(@RequestBody ManageSearchLogisticsOrderDto dto) {
        orderService.logisticsSend(dto);
        return Result.ok();
    }

    @ApiOperation(value = "无需快递的物流发货-批量", notes = "快递订单")
    @PutMapping("/batch/logistics/send")
    public Result batchLogisticsSend(@RequestBody List<ManageSearchLogisticsOrderBatchDto>manageSearchLogisticsOrderBatchDtoList) {
        orderService.batchLogisticsSend(manageSearchLogisticsOrderBatchDtoList);
        return Result.ok();
    }

    @ApiOperation(value = "商家批量处理审核通过订单", notes = "商家批量处理审核通过订单")
    @PutMapping("/approved")
    public Result approvedOrder(@RequestBody Long[] orderIds) {
        orderService.approvedOrder(Arrays.asList(orderIds));
        return Result.ok();
    }

    @GetMapping("/vailDeliveryByOrderId")
    @ApiOperation(value = "验证是否允许发货")
    public Result vailDeliveryByOrderId(@RequestParam(value = "orderId") String orderId){
        Integer result = orderService.vailDeliveryByOrderId(orderId);
        return Result.ok(result);
    }


    @GetMapping("/vailDelivery")
    @ApiOperation(value = "验证是否允许批量发货")
    public Result vailDelivery(@RequestParam(value = "orderIds") String orderIds){
        Integer result = orderService.vailDelivery(orderIds);
        return Result.ok(result);
    }

    @ApiOperation(value = "商家查询订单汇总", notes = "商家查询订单汇总")
    @GetMapping("/manageOrderStatic")
    public Result manageOrderStatic(@Validated ManageOrderStaticDto dto) {
        Page page = orderService.manageOrderStatic(dto);
        return Result.ok(page);
    }

    @ApiOperation(value = "商家查询订单汇总合计", notes = "商家查询订单汇总合计")
    @GetMapping("/manageOrderStaticTotal")
    public Result manageOrderStaticTotal(@Validated ManageOrderStaticDto dto) {
        ManageOrderStaticVo manageOrderStaticVo = orderService.manageOrderStaticTotal(dto);
        return Result.ok(manageOrderStaticVo);
    }

    @ApiOperation(value = "商家查询权益包汇总", notes = "商家查询权益包汇总")
    @GetMapping("/managePackageStatic")
    public Result managePackageStatic(@Validated ManageOrderStaticDto dto) {
        Page page = orderService.managePackageStatic(dto);
        return Result.ok(page);
    }
    @ApiOperation(value = "导出权益包汇总", notes = "导出权益包汇总")
    @GetMapping("/exportPackageStatic")
    public void exportPackageStatic(@Validated ManageOrderStaticDto dto) {
        orderService.exportPackageStatic(dto);
    }
    @ApiOperation(value = "商家查询权益包汇总合计", notes = "商家查询权益包汇总合计")
    @GetMapping("/managePackageStaticTotal")
    public Result managePackageStaticTotal(@Validated ManageOrderStaticDto dto) {
        ManageOrderStaticVo manageOrderStaticVo = orderService.managePackageStaticTotal(dto);
        return Result.ok(manageOrderStaticVo);
    }

    /**
     * 修改订单扣减金额
     * @param dto
     * @return
     */
    @ApiOperation("修改订单扣减金额")
    @PostMapping("/updateOrderDeductionPrice")
    public Result updateOrderDeductionPrice(@RequestBody ManageOrderDeductionPriceDto dto){
        orderService.updateOrderDeductionPrice(dto);
        return Result.ok("修改成功");
    }

    @ApiOperation(value = "查询未给会员产生佣金的订单", notes = "查询未给会员产生佣金的订单")
    @GetMapping("/searchByMiniAccount")
    public Result searchByMiniAccount(@Validated ManageSearchOrderDto dto) {
        PageUtils page = orderService.searchByMiniAccount(dto);
        return Result.ok(page);
    }

}
