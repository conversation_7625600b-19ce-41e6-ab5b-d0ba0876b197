package com.medusa.gruul.order.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: AI Assistant
 * @Description: 评价管理导出Excel VO类
 * @Date: Created in 2025-01-04
 */
@Data
@ApiModel(value = "评价管理导出Excel VO类")
public class ManageEvaluateExcelVo {

    @ApiModelProperty(value = "序号")
    private Long index;

    @ApiModelProperty(value = "评价ID")
    private Long id;

    @ApiModelProperty(value = "订单号")
    private Long orderId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "评价时间")
    private String createTime;

    @ApiModelProperty(value = "评分")
    private Integer rate;

    @ApiModelProperty(value = "商品名称")
    private String productNames;

    @ApiModelProperty(value = "评价内容")
    private String comments;

    @ApiModelProperty(value = "是否有图片")
    private String hasPicture;

    @ApiModelProperty(value = "是否精选")
    private String isChoice;

    @ApiModelProperty(value = "商家回复")
    private String reply;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

}
