package com.medusa.gruul.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccountExtends;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.ManageVerifyPackageGoodsStaticDto;
import com.medusa.gruul.account.api.model.vo.ManageVerifyPackageGoodsStaticVo;
import com.medusa.gruul.afs.api.entity.AfsOrderItem;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.common.dto.CurShopInfoDto;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.ProductStock;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.DeliverTypeEnum;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.WxDeliverStatusEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.BaseOrderMessage;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.order.controller.enums.ExternalOrderEnum;
import com.medusa.gruul.order.mapper.*;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.mq.Sender;
import com.medusa.gruul.order.service.IManageOrderService;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopInfoDto;
import com.medusa.gruul.platform.api.model.vo.StoreFrontOrderVo;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.CommissionRuleTypeEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
@Slf4j
@Service
public class ManageOrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IManageOrderService {

    @Resource
    private Sender sender;
    @Resource
    private MiniOrderServiceImpl miniOrderService;
    @Resource
    private OrderDeliveryMapper orderDeliveryMapper;
    @Resource
    private OrderEvaluateMapper orderEvaluateMapper;
    @Resource
    private OrderProductEvaluateMapper orderProductEvaluateMapper;
    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;
    @Resource
    private RemoteShopsService remoteShopsService;
    @Resource
    private OrderSettingMapper orderSettingMapper;
    @Resource
    private RemoteOrderService remoteOrderService;
    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Autowired
    private AfsOrderItemMapper afsOrderItemMapper;

    @Override
    public PageUtils searchOrder(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_FOUR);
        //订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4.已完成（用户已经签收）, 5.已关闭
        switch (dto.getOrderStatus()) {
            case -1:
                if (ObjectUtil.isNotNull(dto.getSendBillId()) && dto.getSendBillId() == -1) {
                    orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                } else {
                    orderStatusList.clear();
                }
                break;
            case 0:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PAY.getCode());
                orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
                break;
            case 1:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                break;
            case 2:
                orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
                break;
            case 3:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
                break;
            case 4:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
                orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
                break;
            case 5:
                orderStatusList.add(OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.BUYER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.SELLER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.EXCHANGE_SUCCESS_CLOSE.getCode());
                break;
            default:
                break;
        }
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        //近一个月->0; 近三个月->1; 全部->2;
        /*switch (dto.getQuicklyDate()) {
            case 1:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -3).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
            case 2:
                startDate = null;
                endDate = null;
                break;
            case 0:
            default:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -1).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
        }*/
        //判断是否为主店铺
        if(StringUtil.isNotEmpty(dto.getApiShopId())){
            ShopContextHolder.setShopId(dto.getApiShopId());
        }
        String shopId = ShopContextHolder.getShopId();

        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
        Integer mainFlag = shopsPartner.getMainFlag();
        if(mainFlag==1){
            //获取特殊配置
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                Integer platformSearchShopOrder = specialSetting.getPlatformSearchShopOrder();
                if(platformSearchShopOrder==1){
                    List<String> shopIds = remoteMiniInfoService.getSpecialSettingShopIds(shopId);
                    shopIds.add(shopId);
                    dto.setShopIds(shopIds);
                    ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
                }
            }
        }
        if(StringUtil.isNotEmpty(dto.getQueryShopIds())){
            List<String> shopIds2 = new ArrayList<>();
            for (String shopId2 : dto.getQueryShopIds().split(",")) {
                shopIds2.add(shopId2);
            }
            dto.setShopIds2(shopIds2);
        }
        Page<ManageOrderVo> page = baseMapper.searchManageOrderVoPage(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, startDate, endDate, dto);
        List<ManageOrderVo> records = page.getRecords();
        if(records!=null&&records.size()>0){
            for (ManageOrderVo record : records) {
                if(StringUtil.isNotEmpty(record.getWarehouseId())){
                    Warehouse warehouse = remoteGoodsService.getWarehouseById(Long.valueOf(record.getWarehouseId()));
                    record.setWarehouse(warehouse);
                }
                if(StringUtil.isNotEmpty(record.getShopId())){
                    ShopsPartner shopsPartner1 = remoteShopsService.getByShopId(Long.valueOf(record.getShopId()));
                    if(shopsPartner1!=null&&!shopsPartner1.equals("")){
                        record.setShopName(shopsPartner1.getName());
                    }
                }
            }
        }
        ShopContextHolder.setShopId(shopId);
        if(StringUtil.isNotEmpty(dto.getApiShopId())){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        return new PageUtils(page);
    }

    @Override
    public void exportOrder(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_FOUR);
        //订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4.已完成（用户已经签收）, 5.已关闭
        switch (dto.getOrderStatus()) {
            case -1:
                if (ObjectUtil.isNotNull(dto.getSendBillId()) && dto.getSendBillId() == -1) {
                    orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                } else {
                    orderStatusList.clear();
                }
                break;
            case 0:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PAY.getCode());
                orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
                break;
            case 1:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                break;
            case 2:
                orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
                break;
            case 3:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
                break;
            case 4:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
                orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
                break;
            case 5:
                orderStatusList.add(OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.BUYER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.SELLER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.EXCHANGE_SUCCESS_CLOSE.getCode());
                break;
            default:
                break;
        }
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        //近一个月->0; 近三个月->1; 全部->2;
        /*switch (dto.getQuicklyDate()) {
            case 1:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -3).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
            case 2:
                startDate = null;
                endDate = null;
                break;
            case 0:
            default:
                startDate = DateUtil.offsetMonth(DateUtil.date(), -1).toDateStr();
                endDate = DateUtil.date().toDateStr();
                break;
        }*/
        //判断是否为主店铺
        if(StringUtil.isNotEmpty(dto.getApiShopId())){
            ShopContextHolder.setShopId(dto.getApiShopId());
        }
        String shopId = ShopContextHolder.getShopId();

        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
        Integer mainFlag = shopsPartner.getMainFlag();
        if(mainFlag==1){
            //获取特殊配置
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                Integer platformSearchShopOrder = specialSetting.getPlatformSearchShopOrder();
                if(platformSearchShopOrder==1){
                    List<String> shopIds = remoteMiniInfoService.getSpecialSettingShopIds(shopId);
                    shopIds.add(shopId);
                    dto.setShopIds(shopIds);
                    ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
                }
            }
        }
        if(StringUtil.isNotEmpty(dto.getQueryShopIds())){
            List<String> shopIds2 = new ArrayList<>();
            for (String shopId2 : dto.getQueryShopIds().split(",")) {
                shopIds2.add(shopId2);
            }
            dto.setShopIds2(shopIds2);
        }

        dto.setCurrent(1);
        // 设置导出最大数量
        dto.setSize(CommonConstants.MAX_EXPORT_SIZE);

        Page<ManageOrderExcelVo> manageOrderExcelVoPage = baseMapper.searchManageOrderExcelPage(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, startDate, endDate, dto);
        List<ManageOrderExcelVo> excelVoList = manageOrderExcelVoPage.getRecords();
        String fileName = DateUtil.format(new Date(), "yyyyMMdd HHmmss") + RandomUtil.randomString(4) + "提现列表";
        boolean success = HuToolExcelUtils.list2xlsx(excelVoList, fileName, ManageOrderExcelVo.class);
        if(!success){
            throw new ServiceException("导出数据异常", SystemCode.FAILURE.getCode());
        }

    }

    @Override
    public PageUtils searchOrderDetail(ManageOrderItemParam params) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_FOUR);
        //订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4.已完成（用户已经签收）, 5.已关闭
        List<Integer> orderStatus = params.getOrderStatus();
        if (CollectionUtil.isNotEmpty(orderStatus)){
            for (Integer status : orderStatus) {
                switch (status) {
                    case -1:
                        orderStatusList.clear();
                        break;
                    case 0:
                        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PAY.getCode());
                        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
                        break;
                    case 1:
                        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                        break;
                    case 2:
                        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
                        break;
                    case 3:
                        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
                        break;
                    case 4:
                        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
                        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
                        break;
                    case 5:
                        orderStatusList.add(OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE.getCode());
                        orderStatusList.add(OrderStatusEnum.BUYER_CANCEL_CLOSE.getCode());
                        orderStatusList.add(OrderStatusEnum.SELLER_CANCEL_CLOSE.getCode());
                        orderStatusList.add(OrderStatusEnum.EXCHANGE_SUCCESS_CLOSE.getCode());
                        break;
                    default:
                        break;
                }
            }
        }

        params.setOrderStatusList(orderStatusList);

        Page<ManageOrderItemVo> page = baseMapper.searchOrderItem(new Page<>(params.getCurrent(),params.getSize()),params);
        List<ManageOrderItemVo> list =page.getRecords();

        Map<Long, List<AfsOrderItem>> afsOrderItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)){
            List<Long> orderIds = list.stream().map(ManageOrderItemVo::getOrderId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(orderIds)){
                LambdaQueryWrapper<AfsOrderItem> wrapper = new LambdaQueryWrapper<>();
                wrapper.select(AfsOrderItem::getOrderId,AfsOrderItem::getProductQuantity,AfsOrderItem::getRefundAmount);
                wrapper.in(AfsOrderItem::getOrderId,orderIds);
                ShopContextHolder.clear();
                List<AfsOrderItem> afsOrderItems = afsOrderItemMapper.selectList(wrapper);
                if (CollectionUtil.isNotEmpty(afsOrderItems)){
                    afsOrderItemMap  = afsOrderItems.stream().collect(Collectors.groupingBy(AfsOrderItem::getOrderId));
                }

            }
        }
        // 计算退款数
        for (ManageOrderItemVo item : list) {
            BigDecimal refundAmount = BigDecimal.ZERO;
            BigDecimal refundProductQuantity = BigDecimal.ZERO;
            List<AfsOrderItem> afsOrderItemList = afsOrderItemMap.get(item.getOrderId());
            if (CollectionUtil.isNotEmpty(afsOrderItemList)){
                for (AfsOrderItem orderItem : afsOrderItemList) {
                    refundProductQuantity = refundProductQuantity.add(new BigDecimal(orderItem.getProductQuantity()+""));
                    refundAmount = refundAmount.add(orderItem.getRefundAmount());
                }
            }
            item.setRefundRealAmount(refundAmount);
            item.setRefundProductQuantity(refundProductQuantity);
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchExternalOrder(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_TWO);
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        Page<ExternalOrder> page=baseMapper.searchExternalOrder(new Page(dto.getCurrent(), dto.getSize()), orderStatusList);
        List<ExternalOrder> list=page.getRecords();
        List<Long>idList=list.stream().map(ExternalOrder::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateSendStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchExternalOrderByReceived(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_TWO);
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());//待卖家发货
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());//配送中
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());//等待买家取货
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());//等待评价
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());//订单已完成
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());//审核通过
        Page<ExternalOrder> page=baseMapper.searchExternalOrder(new Page(dto.getCurrent(), dto.getSize()), orderStatusList);
        List<ExternalOrder> list=page.getRecords();
        List<Long>idList=list.stream().map(ExternalOrder::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateSendStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils searchOutReceiveOrder(OutReceiveOrderParam param) {
        List<Integer> orderStatusList = new ArrayList<>(CommonConstants.NUMBER_TWO);
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());//待卖家发货
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());//配送中
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());//等待买家取货
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());//等待评价
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());//订单已完成
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());//审核通过
        Page<OutReceiveOrderVo> page = baseMapper.searchOutReceiveOrder(new Page(param.getCurrent(), param.getSize()), orderStatusList);
        List<OutReceiveOrderVo> list = page.getRecords();
        List<Long>idList=list.stream().map(OutReceiveOrderVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            //将发送的数据状态改为已发送
            updateReceiveSyncStatus(idList, ExternalOrderEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeOrder(List<Long> orderIds) {
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        for (Order order : orders) {
            if (!OrderStatusEnum.canCancel(order.getStatus())) {
                throw new ServiceException("当前状态不能取消此订单");
            }
        }
        for (Order order : orders) {
            miniOrderService.cancelOrder(order.getId(), order, OrderStatusEnum.SELLER_CANCEL_CLOSE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliverMessage(Long[] orderIds) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(orderIds!=null&&orderIds.length>0){
            List<Long> list = Arrays.asList(orderIds);
            for (Long orderId : list) {
                OrderVo orderVo = baseMapper.selectOrderVoById(orderId);
                sender.sendDeliveryGoodsMessage(orderVo);
            }
        }else{
            LambdaQueryWrapper<Order>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Order::getStatus,OrderStatusEnum.WAIT_FOR_SEND);
            wrapper.isNotNull(Order::getShopId);
            List<Order> orderList = baseMapper.selectList(wrapper);
            if(orderList!=null&&orderList.size()>0){
                for (Order order : orderList) {
                    OrderVo orderVo = baseMapper.selectOrderVoById(order.getId());
                    sender.sendDeliveryGoodsMessage(orderVo);
                }
            }
        }
        ShopContextHolder.setShopId(shopId);
    }

    @Override
    public Integer vailDeliveryByOrderId(String orderId) {
        Integer result = 0;
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartner();
        Integer mainFlag = shopsPartner.getMainFlag();
        String shopsPartnerShopId = shopsPartner.getShopId();
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        Order order = this.baseMapper.selectById(orderId);
        String shopId = order.getShopId();

        //获取订单特殊配置
        List<SpecialSetting> list = remoteMiniInfoService.getSpecialSettingByShopId(shopId);
        if(list!=null&&list.size()>0){
            SpecialSetting specialSetting = list.get(0);
            Integer platformProxyShipping = specialSetting.getPlatformProxyShipping();
            if(platformProxyShipping==0){//是否允许平台下单
                //为否判断当前订单是否属于当前店铺订单
                if(shopsPartnerShopId.equals(shopId)){
                    result = 1;//允许发货
                }else{
                    result = 2;//其他商户订单不允许发货
                }
            }else{
                //为是判断当前用户是否为主店铺
                if(mainFlag==1){
                    result = 1;
                }else{
                    result = 3;//不是主店铺，不允许发货
                }
            }
        }else{
            ShopContextHolder.setShopId(oldShopId);
            throw new ServiceException("特殊配置不存在，不允许发货");
        }

        ShopContextHolder.setShopId(oldShopId);
        return result;
    }

    @Override
    public Integer vailDelivery(String orderIds) {
        Integer result = 0;
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartner();
        Integer mainFlag = shopsPartner.getMainFlag();
        String shopsPartnerShopId = shopsPartner.getShopId();
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        for (String orderId : orderIds.split(",")) {
            Order order = this.baseMapper.selectById(orderId);
            String shopId = order.getShopId();
            List<SpecialSetting> list = remoteMiniInfoService.getSpecialSettingByShopId(shopId);
            if(list!=null&&list.size()>0){
                SpecialSetting specialSetting = list.get(0);
                Integer platformProxyShipping = specialSetting.getPlatformProxyShipping();
                if(platformProxyShipping==0){//是否允许平台下单
                    //为否判断当前订单是否属于当前店铺订单
                    if(shopsPartnerShopId.equals(shopId)){
                        result = 1;//允许发货
                    }else{
                        ShopContextHolder.setShopId(oldShopId);
                        throw new ServiceException("其他商户订"+orderId+"不允许平台发货");
                    }
                }else{
                    //为是判断当前用户是否为主店铺
                    if(mainFlag==1){
                        result = 1;
                    }else{
                        ShopContextHolder.setShopId(oldShopId);
                        throw new ServiceException("不是主店铺，不允许发货");
                    }
                }
            }else{
                ShopContextHolder.setShopId(oldShopId);
                throw new ServiceException("特殊配置不存在，不允许发货");
            }
        }
        ShopContextHolder.setShopId(oldShopId);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void paymentOrder(List<Long> orderIds) {
        //线下支付可以支付其他门店订单
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        for (Order order : orders) {
            if (!OrderStatusEnum.canPay(order.getStatus())) {
                throw new ServiceException("当前状态不能支付此订单");
            }
        }
        for (Order order : orders) {
            miniOrderService.paymentOrder(order.getId(), order, OrderStatusEnum.WAIT_FOR_SEND);
        }
        ShopContextHolder.setShopId(shopId);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSendStatus(List<Long> orderIds,String sendStatus) {
        baseMapper.updateSendStatus(orderIds,sendStatus);

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReceiveSyncStatus(List<Long> orderIds,String receiveSyncStatus) {
        baseMapper.updateReceiveSyncStatus(orderIds,receiveSyncStatus);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductStock(Long orderId) {
        Order order = baseMapper.selectById(orderId);
//        for (Order order : orders) {
//            if (!OrderStatusEnum.canCancel(order.getStatus())) {
//                throw new ServiceException("当前状态不能取消此订单");
//            }
//        }
//        for (Order order : orders) {
//            miniOrderService.paymentOrder(order.getId(), order, OrderStatusEnum.WAIT_FOR_SEND);
//        }
    }

    @Override
    public void noteOrder(List<Long> orderIds, String note, Boolean isOver) {
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        for (Order order : orders) {
            StringBuffer sb = new StringBuffer();
            if (StrUtil.isBlank(note)) {
                note = "";
            }
            if (!isOver) {
                if (StrUtil.isNotBlank(order.getNote())) {
                    order.setNote(sb.append(order.getNote()).append(StrUtil.CRLF).append(note).toString());
                } else {
                    order.setNote(note);
                }
            } else {
                order.setNote(note);
            }
        }
        this.updateBatchById(orders);
    }


    @Override
    public PageUtils searchOrderEvaluate(ManageSearchEvaluateDto dto) {
        Page<ManageEvaluateVo> page = orderEvaluateMapper.searchOrderEvaluate(new Page(dto.getCurrent(),
                dto.getSize()), dto);
        for (ManageEvaluateVo record : page.getRecords()) {
            record.setRate(record.getShopRate());
        }
        return new PageUtils(page);
    }

    @Override
    public void choiceEvaluate(List<Long> ids) {
        List<OrderProductEvaluate> productEvaluateList = orderProductEvaluateMapper.selectBatchIds(ids);
        for (OrderProductEvaluate productEvaluate : productEvaluateList) {
            productEvaluate.setChoice(true);
            orderProductEvaluateMapper.updateById(productEvaluate);
        }
    }

    @Override
    public void replyEvaluate(Long id, String reply) {
        OrderProductEvaluate productEvaluate = orderProductEvaluateMapper.selectById(id);
        productEvaluate.setReply(reply);
        orderProductEvaluateMapper.updateById(productEvaluate);
    }

    @Override
    public OrderVo orderInfo(Long orderId) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        OrderVo orderVo = baseMapper.selectOrderVoById(orderId);
        String shopId = orderVo.getShopId();
        ShopContextHolder.setShopId(shopId);

        if(orderVo!=null&&orderVo.getWarehouseId()!=null){
            Warehouse warehouse = remoteGoodsService.getWarehouseById(orderVo.getWarehouseId());
            if(warehouse!=null){
                orderVo.setWarehouseAddress(warehouse.getWarehouseAddress());
                orderVo.setWarehouseFullName(warehouse.getWarehouseFullName());
            }
        }
        if(orderVo!=null&&orderVo.getStoreFrontId()!=null){
            StoreFrontOrderVo storeFrontOrderVo = remoteMiniInfoService.getPlatformDepartmentByStoreFrontId(orderVo.getStoreFrontId());
            if(storeFrontOrderVo!=null){
                orderVo.setStoreFrontName(storeFrontOrderVo.getStoreFrontName());
                orderVo.setStoreFrontCode(storeFrontOrderVo.getStoreFrontCode());
            }
        }
        ShopContextHolder.setShopId(oldShopId);
        return orderVo;
    }

    @Override
    public void unChoiceEvaluate(List<Long> ids) {
        List<OrderProductEvaluate> productEvaluateList = orderProductEvaluateMapper.selectBatchIds(ids);
        for (OrderProductEvaluate productEvaluate : productEvaluateList) {
            if (productEvaluate.getChoice()) {
                productEvaluate.setChoice(false);
                orderProductEvaluateMapper.updateById(productEvaluate);
            }
        }
    }

    @Override
    public PageUtils searchLogisticsOrderList(ManageLogisticsOrderDto dto) {
        Page<ManageDeliveryOrderVo> page = baseMapper.searchLogisticsOrderList(new Page(dto.getCurrent(),
                dto.getSize()), dto);
        return new PageUtils(page);

    }

    @Override
    public ManageOrderOverviewVo getOverview() {
        ManageOrderOverviewVo vo = new ManageOrderOverviewVo();
        Integer waitForPay = baseMapper.selectCount(new LambdaQueryWrapper<Order>().eq(Order::getStatus,
                OrderStatusEnum.WAIT_FOR_PAY));
        Integer waitForSend = baseMapper.selectCount(new LambdaQueryWrapper<Order>().eq(Order::getStatus,
                OrderStatusEnum.WAIT_FOR_SEND));
        Integer shipped = baseMapper.selectCount(new LambdaQueryWrapper<Order>().eq(Order::getStatus,
                OrderStatusEnum.SHIPPED));
        Integer waitForPickup = baseMapper.selectCount(new LambdaQueryWrapper<Order>().eq(Order::getStatus,
                OrderStatusEnum.WAIT_FOR_PICKUP));
        vo.setWaitForPay(waitForPay);
        vo.setWaitForSend(waitForSend);
        vo.setShipped(shipped);
        vo.setWaitForPickup(waitForPickup);
        return vo;
    }
    @Override
    public ManageOrderRealTimeOverviewVo getRealTimeOverview(String startDate,String endDate,Integer type) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Date date1 = dateFormat.parse(startDate);
        Date date2 = dateFormat.parse(endDate);

        startDate =dateFormat.format(date1);
        startDate=startDate+" 00:00:00";
        endDate =dateFormat.format(date2);
        endDate=endDate+" 23:59:59";

        List<ManageOrderTradeDto> dbTradeDtoList = baseMapper.transacTionOverview(startDate, endDate, OrderStatusEnum.getPaidStatus());
        List<ManageOrderTradeDto> tradeDtoList = new ArrayList<>();
        while(date1.compareTo(date2) <= 0){
            Date finalDate = date1;
            List<ManageOrderTradeDto> existList = dbTradeDtoList.stream().filter(e -> {
                try {
                    return dateFormat.parse(e.getDate()).compareTo(finalDate) == 0;
                } catch (ParseException parseException) {
                    parseException.printStackTrace();
                }
                return false;
            }).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(existList)){
                ManageOrderTradeDto newDto = new ManageOrderTradeDto();
                newDto.setDate(dateFormat.format(date1));
                newDto.setTransactionVolume(0);
                newDto.setTurnover(new BigDecimal("0"));
                tradeDtoList.add(newDto);
            }else{
                tradeDtoList.addAll(existList);
            }
            date1 = DateUtil.offsetDay(date1, 1);
        }
        Integer num=0;
        HashMap<String,String> map=new HashMap<>();
//       获取当前日期
        Date date = new Date();
        String newData= dateFormat.format(date);
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = c.getTime();
        //昨天
        String yesterDay = dateFormat.format(yesterday);

        ManageOrderRealTimeOverviewVo manageOrderRealTimeOverviewVo=new ManageOrderRealTimeOverviewVo();
        manageOrderRealTimeOverviewVo.setUpdateTime(date);
        ManageOrderTradeDto trade=new ManageOrderTradeDto();
        ManageOrderTradeDto yesterdayTrade=new ManageOrderTradeDto();
        if(type==1){
            //今天
            map.put("startDate",newData+" 00:00:00");
            map.put("endDate",newData+" 23:59:59");
            num=remoteMiniAccountService.getFootprintNumber(map);
            trade= baseMapper.transacTion(newData+" 00:00:00",newData+" 23:59:59", OrderStatusEnum.getPaidStatus());
            trade.setViews(num);

            Integer addCustom = remoteMiniAccountService.getAddCustom(newData + " 00:00:00", newData + " 23:59:59");
            trade.setAddCustom(addCustom);

            Integer againBuyCustom = getAgainBuyCustom(newData + " 00:00:00", newData + " 23:59:59");
            trade.setAgainBuyCustom(againBuyCustom);
            //昨天
            map.put("startDate",yesterDay+" 00:00:00");
            map.put("endDate",yesterDay+" 23:59:59");
            Integer yesterNum=remoteMiniAccountService.getFootprintNumber(map);
            yesterdayTrade=baseMapper.transacTion(yesterDay+" 00:00:00",yesterDay+" 23:59:59", OrderStatusEnum.getPaidStatus());
            yesterdayTrade.setViews(yesterNum);

            Integer addCustom2 = remoteMiniAccountService.getAddCustom(yesterDay + " 00:00:00", yesterDay + " 23:59:59");
            yesterdayTrade.setAddCustom(addCustom2);

            Integer againBuyCustom2 = getAgainBuyCustom(yesterDay + " 00:00:00", yesterDay + " 23:59:59");
            yesterdayTrade.setAgainBuyCustom(againBuyCustom2);

        }else {
            //查询日期的总和
            map.put("startDate",startDate);
            map.put("endDate",endDate);
            num=remoteMiniAccountService.getFootprintNumber(map);
            trade= baseMapper.transacTion(startDate,endDate, OrderStatusEnum.getPaidStatus());
            trade.setViews(num);

            Integer addCustom = remoteMiniAccountService.getAddCustom(startDate, endDate);
            trade.setAddCustom(addCustom);

            Integer againBuyCustom = getAgainBuyCustom(startDate, endDate);
            trade.setAgainBuyCustom(againBuyCustom);
        }
        manageOrderRealTimeOverviewVo.setTradeLineVos(tradeDtoList);
        manageOrderRealTimeOverviewVo.setYesterdayTrade(yesterdayTrade);
        manageOrderRealTimeOverviewVo.setTrade(trade);
        return manageOrderRealTimeOverviewVo;
    }


    @Override
    public List<ManageOrderVo> searchLogisticsOrder(ManageSearchLogisticsOrderDto dto) {
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<Long> orderIds = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getOrderIds())) {
            orderIds =
                    Arrays.stream(dto.getOrderIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        }

        List<ManageOrderVo> list = baseMapper.searchLogisticsOrder(orderIds);
        ShopContextHolder.setShopId(oldShopId);
        return list;
    }

    @Override
    public Integer countLogisticsWaitSend() {
        Integer waitForSend = baseMapper.countLogisticsWaitSend();
        return waitForSend;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logisticsSend(ManageSearchLogisticsOrderDto dto) {
        List<Long> orderIds =
                Arrays.stream(dto.getOrderIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        //修改库存
        for (int i = 0; i < orderIds.size(); i++) {
            List<ProductStock> stockList = new ArrayList<>();
            List<OrderItem> orderItemList = remoteOrderService.orderItemByOrderIds(orderIds.get(i));
            Map orderItemMap = new HashMap();
            List<Long> skuIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderItemList)) {
                skuIds = orderItemList.stream().map(OrderItem::getProductSkuId).collect(Collectors.toList());
                orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItem::getProductSkuId, v -> v));
            }
            List<ProductStock> productStockList = remoteGoodsService.productStockBySkuIds(skuIds, dto.getWarehouseId());
            if (CollectionUtils.isEmpty(productStockList)) {
                throw new ServiceException("库存数量不足，发货失败！");
            }
            for (int j = 0; j < productStockList.size(); j++) {
                ProductStock productStock = productStockList.get(j);
                OrderItem orderItem = (OrderItem) orderItemMap.get(productStock.getSkuId());
                //库存数据
                Integer stock = productStock.getStock().intValue();
                //订单数量
                Integer quantity = orderItem.getProductQuantity();
                //剩余数量
                Integer nuber = stock - quantity;
                productStock.setStock(new BigDecimal(nuber));
                stockList.add(productStock);
                if (nuber <= 0) {
                    throw new ServiceException("库存数量不足，发货失败！");
                }
            }
            boolean success=remoteGoodsService.batchProductSubtractStock(stockList);
            boolean fl=remoteOrderService.updateOrderWarehouse(orderIds.get(i),dto.getWarehouseId());
            if(!success && !fl){
                throw new ServiceException("修改库存失败");
            }
        }
        //获取订单设置
        OrderSetting orderSetting = orderSettingMapper.selectOne(null);
        OrderDelivery orderDelivery = new OrderDelivery();
        //订单收货
        orderDelivery.setDeliveryCompany("无");
        orderDelivery.setDeliverySn("无");
        orderDelivery.setDeliveryTime(LocalDateTime.now());
        orderDeliveryMapper.update(orderDelivery, new LambdaQueryWrapper<OrderDelivery>().in(OrderDelivery::getOrderId,
                orderIds));
        Order order = new Order();
        order.setStatus(OrderStatusEnum.WAIT_FOR_PICKUP);
        baseMapper.update(order, new LambdaQueryWrapper<Order>().in(Order::getId, orderIds));

        for (Long orderId : orderIds) {
            OrderVo vo = baseMapper.selectOrderVoById(orderId);
            //查询会员持有的积分、收货地址
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(vo.getUserId(), Arrays.asList(4));
            sender.sendDeliveryMessage(vo, accountInfoDto.getMiniAccountOauths().getOpenId());
            BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
            baseOrderMessage.setOrderId(vo.getId());
            baseOrderMessage.setShopId(vo.getShopId());
            baseOrderMessage.setTenantId(vo.getTenantId());
            sender.sendAutoReceiptOrderMessage(baseOrderMessage,
                    orderSetting.getConfirmOvertime() * TimeConstants.ONE_DAY);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchLogisticsSend(List<ManageSearchLogisticsOrderBatchDto> manageSearchLogisticsOrderBatchDtoList) {
        for (ManageSearchLogisticsOrderBatchDto manageSearchLogisticsOrderBatchDto : manageSearchLogisticsOrderBatchDtoList) {
            String shopId = ShopContextHolder.getShopId();
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
            //修改库存
            List<ProductStock> stockList = new ArrayList<>();
            List<OrderItem> orderItemList = remoteOrderService.orderItemByOrderIds(Long.valueOf(manageSearchLogisticsOrderBatchDto.getOrderId()));
            Map orderItemMap = new HashMap();
            List<Long> skuIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderItemList)) {
                skuIds = orderItemList.stream().map(OrderItem::getProductSkuId).collect(Collectors.toList());
                orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItem::getProductSkuId, v -> v));
            }
            List<ProductStock> productStockList = remoteGoodsService.productStockBySkuIds(skuIds, manageSearchLogisticsOrderBatchDto.getWarehouseId());
            if (CollectionUtils.isEmpty(productStockList)) {
                throw new ServiceException("库存数量不足，发货失败！");
            }
            for (int j = 0; j < productStockList.size(); j++) {
                ProductStock productStock = productStockList.get(j);
                OrderItem orderItem = (OrderItem) orderItemMap.get(productStock.getSkuId());
                //库存数据
                Integer stock = productStock.getStock().intValue();
                //订单数量
                Integer quantity = orderItem.getProductQuantity();
                //剩余数量
                Integer nuber = stock - quantity;
                productStock.setStock(new BigDecimal(nuber));
                stockList.add(productStock);
                if (nuber <= 0) {
                    throw new ServiceException("库存数量不足，发货失败！");
                }
            }
            boolean success=remoteGoodsService.batchProductSubtractStock(stockList);
            boolean fl=remoteOrderService.updateOrderWarehouse(Long.valueOf(manageSearchLogisticsOrderBatchDto.getOrderId()),manageSearchLogisticsOrderBatchDto.getWarehouseId());
            if(!success && !fl){
                throw new ServiceException("修改库存失败");
            }
            //获取订单设置
            OrderSetting orderSetting = orderSettingMapper.selectOne(null);
            OrderDelivery orderDelivery = new OrderDelivery();
            //订单收货
            orderDelivery.setDeliveryCompany("无");
            orderDelivery.setDeliverySn("无");
            orderDelivery.setDeliveryTime(LocalDateTime.now());
            orderDelivery.setDeliveryType(DeliverTypeEnum.NO);
            orderDeliveryMapper.update(orderDelivery, new LambdaQueryWrapper<OrderDelivery>().eq(OrderDelivery::getOrderId,
                    manageSearchLogisticsOrderBatchDto.getOrderId()));
            Order order = new Order();
            order.setStatus(OrderStatusEnum.WAIT_FOR_PICKUP);
            baseMapper.update(order, new LambdaQueryWrapper<Order>().eq(Order::getId, manageSearchLogisticsOrderBatchDto.getOrderId()));
            OrderVo vo = baseMapper.selectOrderVoById(Long.valueOf(manageSearchLogisticsOrderBatchDto.getOrderId()));
            //查询会员持有的积分、收货地址
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(vo.getUserId(), Arrays.asList(4));
            sender.sendDeliveryMessage(vo, accountInfoDto.getMiniAccountOauths().getOpenId());
            BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
            baseOrderMessage.setOrderId(vo.getId());
            baseOrderMessage.setShopId(vo.getShopId());
            baseOrderMessage.setTenantId(vo.getTenantId());
            sender.sendAutoReceiptOrderMessage(baseOrderMessage,
                    orderSetting.getConfirmOvertime() * TimeConstants.ONE_DAY);
            //订单发货消息
            sender.sendShippedOrderMessage(vo);

            ShopContextHolder.setShopId(shopId);
        }
    }
    /**
     * 商家批量修改审核通过订单
     * @param orderIds the order ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvedOrder(List<Long> orderIds) {
        List<Order> orders = baseMapper.selectBatchIds(orderIds);
        for (Order order : orders) {
            if (!OrderStatusEnum.canApproved(order.getStatus())) {
                throw new ServiceException("当前状态不能审核此订单[" + order.getId() + "]");
            }
        }
        for (Order order : orders) {
            order.setStatus(OrderStatusEnum.APPROVED);
            order.setApprovedTime(LocalDateTime.now());
        }
        this.updateBatchById(orders);
    }

    @Override
    public List<Order> getOrderByWxDeliverStatus() {
        LambdaQueryWrapper<Order>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getWxDeliverStatusEnum, WxDeliverStatusEnum.DELIVER);
        List<Order> list = this.list(wrapper);
        return list;
    }

    @Override
    public Integer getAgainBuyCustom(String startDate, String endDate) {

        List<Map> list = this.baseMapper.getAgainBuyCustom(startDate, endDate);
        if(list!=null&&list.size()>0){
            return list.size();
        }else{
            return 0;
        }
    }

    @Override
    public BigDecimal getCommissionByOrderId(Long orderId,Integer type) {
        BigDecimal amount = BigDecimal.ZERO;
        List<OrderItem> orderItemList = orderItemMapper.selectByOrderId(orderId);
        if(orderItemList!=null&&orderItemList.size()>0){
            for (OrderItem orderItem : orderItemList) {
                Long productId = orderItem.getProductId();
                ProductVo product = remoteGoodsService.findProductById(productId);
                Integer ruleType = product.getRuleType();
                Integer productQuantity = orderItem.getProductQuantity();
                if(ruleType!=null){
                    if(type==CommonConstants.NUMBER_ZERO){
                        if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                            BigDecimal parentReceive = product.getParentReceive();
                            if(parentReceive!=null){
                                amount = amount.add(parentReceive.multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                        if(ruleType == CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                            BigDecimal productPrice = orderItem.getProductPrice();
                            BigDecimal parentReceive = product.getParentReceive();
                            if(parentReceive!=null){
                                amount = amount.add(parentReceive.divide(new BigDecimal(100)).multiply(productPrice).multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                    }
                    if(type==CommonConstants.NUMBER_ONE){
                        if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                            BigDecimal aboveParentReceive = product.getAboveParentReceive();
                            if(aboveParentReceive!=null){
                                amount = amount.add(aboveParentReceive.multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                        if(ruleType == CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                            BigDecimal productPrice = orderItem.getProductPrice();
                            BigDecimal aboveParentReceive = product.getAboveParentReceive();
                            if(aboveParentReceive!=null){
                                amount = amount.add(aboveParentReceive.divide(new BigDecimal(100)).multiply(productPrice).multiply(BigDecimal.valueOf(productQuantity)));
                            }
                        }
                    }
                }
            }
        }

        return amount;
    }

    /**
     * 按门店、员工统计订单数量和金额
     * @param manageOrderStaticDto
     * @return
     */
    @Override
    public Page manageOrderStatic(ManageOrderStaticDto manageOrderStaticDto) {
        if(null == manageOrderStaticDto || (StrUtil.isBlank(manageOrderStaticDto.getParentCode()) && StrUtil.isBlank(manageOrderStaticDto.getStoreName()))){
            manageOrderStaticDto.setFirstClassFlag(1);
        }
        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
        manageOrderStaticDto.setOrderStatusList(orderStatusList);
        manageOrderStaticDto.setType(0);
        if(StrUtil.isNotBlank(manageOrderStaticDto.getEndTime())){
            manageOrderStaticDto.setEndTime(manageOrderStaticDto.getEndTime() + " 23:59:59");
        }
        if(StrUtil.isNotBlank(manageOrderStaticDto.getParentCode())){
            // 查询门店编码是否是末级，如果是末级，则表示查询的是员工的统计数据
            StoreFrontOrderVo storeFrontOrderVo = remoteMiniInfoService.getStoreFrontByClassCode(manageOrderStaticDto.getParentCode());
            if(null != storeFrontOrderVo && "0".equalsIgnoreCase(storeFrontOrderVo.getIsCatalog())){
                manageOrderStaticDto.setType(1);
            }
        }
        if(StrUtil.isBlank(manageOrderStaticDto.getParentCode())){
            manageOrderStaticDto.setParentCode("-1");
        }
        Page<ManageOrderStaticVo> pageVo = baseMapper.manageOrderStatic(new Page(manageOrderStaticDto.getCurrent(), manageOrderStaticDto.getSize()),manageOrderStaticDto);
        // 查询门店、员工的核销数量
        ManageVerifyPackageGoodsStaticDto dto = new ManageVerifyPackageGoodsStaticDto();
        dto.setType(manageOrderStaticDto.getType());
        if(dto.getType() == 0){
            // 门店，获取pageVo.getRecords()里的id组成list
            dto.setClassCodeList(pageVo.getRecords().stream().map(ManageOrderStaticVo::getId).collect(Collectors.toList()));
        }else{
            // 员工
            dto.setEmployeeIdList(pageVo.getRecords().stream().map(ManageOrderStaticVo::getId).collect(Collectors.toList()));
        }
        dto.setPackageName(manageOrderStaticDto.getPackageName());
        dto.setStartTime(manageOrderStaticDto.getStartTime());
        dto.setEndTime(manageOrderStaticDto.getEndTime());
        List<ManageVerifyPackageGoodsStaticVo> manageVerifyPackageGoodsStaticVos = remoteMiniAccountService.listVerifyPackageCodeCount(dto);
        if(CollectionUtil.isNotEmpty(manageVerifyPackageGoodsStaticVos)){
            // 将核销记录按照id、核销次数放入map
            Map<String, Integer> verifyPackageGoodsStaticMap = manageVerifyPackageGoodsStaticVos.stream().collect(Collectors.toMap(ManageVerifyPackageGoodsStaticVo::getId, ManageVerifyPackageGoodsStaticVo::getPackageVerifyQty));
            // 循环pageVo.getRecords()，将核销次数放入pageVo.getRecords()
            pageVo.getRecords().forEach(e -> {
                if(verifyPackageGoodsStaticMap.containsKey(e.getId())){
                    e.setPackageVerifyQty(verifyPackageGoodsStaticMap.get(e.getId()));
                }
            });
        }

        return pageVo;
    }

    /**
     * 按门店、员工统计订单数量和金额的合计值
     * @param manageOrderStaticDto
     * @return
     */
    @Override
    public ManageOrderStaticVo manageOrderStaticTotal(ManageOrderStaticDto manageOrderStaticDto) {
        if(null == manageOrderStaticDto || (StrUtil.isBlank(manageOrderStaticDto.getParentCode()) && StrUtil.isBlank(manageOrderStaticDto.getStoreName()))){
            manageOrderStaticDto.setFirstClassFlag(1);
        }
        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
        manageOrderStaticDto.setOrderStatusList(orderStatusList);
        manageOrderStaticDto.setType(0);
        if(StrUtil.isNotBlank(manageOrderStaticDto.getEndTime())){
            manageOrderStaticDto.setEndTime(manageOrderStaticDto.getEndTime() + " 23:59:59");
        }
        if(StrUtil.isNotBlank(manageOrderStaticDto.getParentCode())){
            // 查询门店编码是否是末级，如果是末级，则表示查询的是员工的统计数据
            StoreFrontOrderVo storeFrontOrderVo = remoteMiniInfoService.getStoreFrontByClassCode(manageOrderStaticDto.getParentCode());
            if(null != storeFrontOrderVo && "0".equalsIgnoreCase(storeFrontOrderVo.getIsCatalog())){
                manageOrderStaticDto.setType(1);
            }
        }
        if(StrUtil.isBlank(manageOrderStaticDto.getParentCode())){
            manageOrderStaticDto.setParentCode("-1");
        }
        Page<ManageOrderStaticVo> pageVo = baseMapper.manageOrderStatic(new Page(manageOrderStaticDto.getCurrent(), manageOrderStaticDto.getSize()),manageOrderStaticDto);
        // 查询门店、员工的核销数量
        ManageVerifyPackageGoodsStaticDto dto = new ManageVerifyPackageGoodsStaticDto();
        dto.setType(manageOrderStaticDto.getType());
        if(dto.getType() == 0){
            // 门店，获取pageVo.getRecords()里的id组成list
            dto.setClassCodeList(pageVo.getRecords().stream().map(ManageOrderStaticVo::getId).collect(Collectors.toList()));
        }else{
            // 员工
            dto.setEmployeeIdList(pageVo.getRecords().stream().map(ManageOrderStaticVo::getId).collect(Collectors.toList()));
        }
        dto.setPackageName(manageOrderStaticDto.getPackageName());
        dto.setStartTime(manageOrderStaticDto.getStartTime());
        dto.setEndTime(manageOrderStaticDto.getEndTime());
        // 取合计记录
        ManageOrderStaticVo manageOrderStaticVoTotal = baseMapper.manageOrderStaticTotal(manageOrderStaticDto);
        ManageVerifyPackageGoodsStaticVo manageVerifyPackageGoodsStaticVos = remoteMiniAccountService.verifyPackageCodeTotal(dto);
        if(null != manageOrderStaticVoTotal && null != manageVerifyPackageGoodsStaticVos){
            manageOrderStaticVoTotal.setPackageVerifyQty(manageVerifyPackageGoodsStaticVos.getPackageVerifyQty());
        }
        return manageOrderStaticVoTotal;
    }

    /**
     * 按权益包统计订单数量和金额
     * @param manageOrderStaticDto
     * @return
     */
    @Override
    public Page managePackageStatic(ManageOrderStaticDto manageOrderStaticDto) {

        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
        manageOrderStaticDto.setOrderStatusList(orderStatusList);
        if(StrUtil.isNotBlank(manageOrderStaticDto.getEndTime())){
            manageOrderStaticDto.setEndTime(manageOrderStaticDto.getEndTime() + " 23:59:59");
        }
        Page<ManageOrderStaticVo> pageVo = baseMapper.managePackageStatic(new Page(manageOrderStaticDto.getCurrent(), manageOrderStaticDto.getSize()),manageOrderStaticDto);
        return pageVo;
    }
    @Override
    public void exportPackageStatic(ManageOrderStaticDto param){
        HuToolExcelUtils.exportParamToMax(param);
        Page page = this.managePackageStatic(param);
        HuToolExcelUtils.exportData(page.getRecords(),"权益包汇总",source->new ManageOrderStaticExcelVo());
    }
    /**
     * 按权益包统计订单数量和金额的合计值
     * @param manageOrderStaticDto
     * @return
     */
    @Override
    public ManageOrderStaticVo managePackageStaticTotal(ManageOrderStaticDto manageOrderStaticDto) {

        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
        manageOrderStaticDto.setOrderStatusList(orderStatusList);
        if(StrUtil.isNotBlank(manageOrderStaticDto.getEndTime())){
            manageOrderStaticDto.setEndTime(manageOrderStaticDto.getEndTime() + " 23:59:59");
        }
        // 取合计记录
        ManageOrderStaticVo manageOrderStaticVoTotal = baseMapper.managePackageStaticTotal(manageOrderStaticDto);
        return manageOrderStaticVoTotal;
    }

    @Override
    public void updateOrderDeductionPrice(ManageOrderDeductionPriceDto dto) {
        if(dto.getOrderId()!=null){
            CurUserDto curUser= CurUserUtil.getHttpCurUser();
            Order order = this.getById(dto.getOrderId());
            order.setDeductionPrice(dto.getDeductionPrice());
            order.setDeductionUser(curUser.getUserId());
            order.setDeductionTime(LocalDateTime.now());
            BigDecimal totalAmount = order.getTotalAmount();
            BigDecimal promotionAmount = order.getPromotionAmount();//运费金额
            BigDecimal youhuiPrice = order.getYouhuiPrice();//优惠金额
            //应付金额（实际支付金额）=订单总金额-促销优化金额+运费
            BigDecimal payAmount = NumberUtil.sub(totalAmount, promotionAmount);
            //减掉优惠券金额
            payAmount = NumberUtil.sub(payAmount, youhuiPrice);
            //减掉扣减金额
            payAmount = NumberUtil.sub(payAmount, dto.getDeductionPrice());
            order.setYouhuiPrice(youhuiPrice);

            BigDecimal freightAmount = order.getFreightAmount();//运费金额
            if (payAmount.compareTo(BigDecimal.ZERO) == -1) {
                payAmount = BigDecimal.ZERO.add(freightAmount);
            } else {
                payAmount = payAmount.add(freightAmount);
            }
            //支付金额异常
            if (NumberUtil.isLess(payAmount, OrderConstant.MIN_PAY_FEE)) {
                log.error("订单{},支付金额:{},总金额:{},运费:{}", dto.toString(), payAmount,
                        totalAmount, 0, 0, freightAmount);
                payAmount = OrderConstant.MIN_PAY_FEE;
            }

            log.info("订单{},支付金额:{},总金额:{},运费:{}", dto.toString(), payAmount,
                    totalAmount, 0, 0, freightAmount);
            //实际金额=应付金额-退款金额
            order.setDiscountsAmount(payAmount.setScale(2, BigDecimal.ROUND_DOWN));
            order.setPayAmount(payAmount.setScale(2, BigDecimal.ROUND_DOWN));
            this.updateById(order);
        }else{
            throw new ServiceException("订单id不能为空");
        }
    }

    @Override
    public Integer getOrderCountByStoreFrontCode(String storeFrontCode) {
        LambdaQueryWrapper<Order>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getStoreFrontCode,storeFrontCode);
        int count = this.count(queryWrapper);
        return count;
    }

    /**
     * 查询未给会员用户产生佣金且状态为完成的订单
     * @param dto the dto
     * @return
     */
    @Override
    public PageUtils searchByMiniAccount(ManageSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(1);
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        if(StrUtil.isNotBlank(dto.getMiniAccountShopUserId())){
            MiniAccountExtends miniAccountExtends = this.remoteMiniAccountService.getMiniAccountExtends(dto.getMiniAccountShopUserId());
            if(null != miniAccountExtends){
                dto.setMiniAccountShopUserId(miniAccountExtends.getShopUserId());
            }
        }

        String shopId = ShopContextHolder.getShopId();

        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        Page<Order> page = baseMapper.searchByMiniAccount(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, startDate, endDate, dto);
        ShopContextHolder.setShopId(shopId);

        return new PageUtils(page);
    }

    @Override
    public void exportOrderDetail(ManageOrderItemParam params) {
        // 设置导出最大数量
        params.setCurrent(1);
        params.setSize(CommonConstants.MAX_EXPORT_SIZE);

        // 直接调用现有的查询方法
        PageUtils pageUtils = this.searchOrderDetail(params);
        List<ManageOrderItemVo> orderItemList = pageUtils.getList();

        // 转换为导出VO并添加序号
        List<ManageOrderItemExcelVo> excelVoList = new ArrayList<>();
        for (int i = 0; i < orderItemList.size(); i++) {
            ManageOrderItemVo item = orderItemList.get(i);
            ManageOrderItemExcelVo excelVo = new ManageOrderItemExcelVo();

            // 设置序号
            excelVo.setIndex((long) (i + 1));
            // 复制基本字段
            excelVo.setOrderId(item.getOrderId().toString());
            excelVo.setProductName(item.getProductName());
            excelVo.setProductPrice(item.getProductPrice());
            excelVo.setProductQuantity(item.getProductQuantity());
            excelVo.setRealAmount(item.getRealAmount());
            excelVo.setStoreFrontName(item.getStoreFrontName());
            excelVo.setNikeName(item.getNikeName());
            excelVo.setPhone(item.getPhone());
            excelVo.setAccountName(item.getAccountName());
            excelVo.setWarehouseName(item.getWarehouseName());

            // 转换发货方式枚举为中文
            if (item.getDeliveryType() != null) {
                switch (item.getDeliveryType()) {
                    case MANUAL_DELIVERY:
                        excelVo.setDeliveryTypeName("手动发货");
                        break;
                    case LOGISTICS_DELIVERY:
                        excelVo.setDeliveryTypeName("物流配送");
                        break;
                    case HOME_DELIVERY:
                        excelVo.setDeliveryTypeName("送货上门");
                        break;
                    case NO_LOGISTICS:
                        excelVo.setDeliveryTypeName("无需物流配送");
                        break;
                    case SELF_PICKUP:
                        excelVo.setDeliveryTypeName("自提");
                        break;
                    default:
                        excelVo.setDeliveryTypeName("");
                        break;
                }
            } else {
                excelVo.setDeliveryTypeName("");
            }

            excelVoList.add(excelVo);
        }

        String fileName = "订单明细列表";
        HuToolExcelUtils.exportData(excelVoList, fileName, null);
    }

}
